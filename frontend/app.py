"""
Smart Customer Support Orchestrator Frontend
Streamlit application showcasing the brain system and multi-agent capabilities
"""

import streamlit as st
import requests
import json
import time
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import pandas as pd
from typing import Dict, Any, List

# Compatibility function for st.rerun()
def rerun_app():
    """Compatibility function for st.rerun() that works with different Streamlit versions."""
    try:
        # Try the new method first (Streamlit >= 1.18.0)
        st.rerun()
    except AttributeError:
        try:
            # Fall back to experimental method (older versions)
            st.experimental_rerun()
        except AttributeError:
            # If neither works, use a workaround
            st.session_state._rerun_requested = True

# Page configuration
st.set_page_config(
    page_title="🧠 Smart Customer Support Orchestrator",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .brain-state {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .agent-status {
        padding: 0.5rem;
        border-radius: 0.25rem;
        margin: 0.25rem 0;
        font-weight: bold;
    }
    .agent-active {
        background-color: #d4edda;
        color: #155724;
    }
    .agent-thinking {
        background-color: #fff3cd;
        color: #856404;
    }
    .agent-idle {
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'conversation_history' not in st.session_state:
    st.session_state.conversation_history = []

if 'active_conversation' not in st.session_state:
    st.session_state.active_conversation = None

if 'conversation_id' not in st.session_state:
    st.session_state.conversation_id = None

if 'awaiting_follow_up' not in st.session_state:
    st.session_state.awaiting_follow_up = False
if 'brain_states' not in st.session_state:
    st.session_state.brain_states = {
        'agent_alpha': {'consciousness': 0.85, 'stress': 0.3, 'empathy': 0.8, 'learning': True},
        'agent_beta': {'consciousness': 0.78, 'stress': 0.2, 'empathy': 0.9, 'learning': True},
        'agent_gamma': {'consciousness': 0.92, 'stress': 0.4, 'empathy': 0.7, 'learning': False},
        'agent_delta': {'consciousness': 0.88, 'stress': 0.1, 'empathy': 0.85, 'learning': True}
    }
if 'swarm_active' not in st.session_state:
    st.session_state.swarm_active = False
if 'collective_session' not in st.session_state:
    st.session_state.collective_session = None

# Backend API configuration
BACKEND_URL = "http://localhost:8080"  # Updated to match our API server

def call_backend_api(endpoint: str, data: Dict[Any, Any]) -> Dict[Any, Any]:
    """Call the backend API with error handling."""
    try:
        response = requests.post(f"{BACKEND_URL}{endpoint}", json=data, timeout=30)
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"API call failed with status {response.status_code}"}
    except requests.exceptions.RequestException as e:
        # Return mock data for demo purposes when backend is not available
        return get_mock_response(endpoint, data)

def get_mock_response(endpoint: str, data: Dict[Any, Any]) -> Dict[Any, Any]:
    """Generate mock responses for demo purposes."""
    if endpoint == "/process-message":
        content = data.get("content", "").lower()
        is_follow_up = data.get("is_follow_up", False)

        # Generate contextual responses based on content
        if "money" in content or "refund" in content:
            response = "I understand you're concerned about your money. Let me help you with that refund request. Can you please provide your order number so I can look into this immediately?"
            follow_ups = ["What was your order number?", "When did you place the order?", "What payment method did you use?"]
        elif "idiot" in content or "stupid" in content:
            response = "I apologize if you've had a frustrating experience. I'm here to help resolve your issue. Could you please tell me more about what's happening so I can assist you better?"
            follow_ups = ["What specific issue are you experiencing?", "How can I make this right for you?", "Would you like me to escalate this to a supervisor?"]
        elif is_follow_up:
            response = "Thank you for that additional information. Based on what you've told me, I can help you further. Is there anything else you'd like me to clarify or assist with?"
            follow_ups = ["Is there anything else I can help with?", "Would you like me to send you a confirmation email?", "How would you rate your experience today?"]
        else:
            response = "Thank you for contacting us. I understand your concern and I'm here to help resolve this issue promptly."
            follow_ups = ["Can you provide more details?", "When did this issue start?", "Have you tried any troubleshooting steps?"]

        return {
            "workflow_id": f"workflow_{int(time.time())}",
            "processing_time": 2.3,
            "agents_involved": ["intake", "knowledge", "resolution", "quality"],
            "final_response": response,
            "follow_up_questions": follow_ups,
            "conversation_continues": True,
            "brain_state_updates": {
                "emotional_analysis": {"sentiment": "negative" if any(word in content for word in ["idiot", "stupid", "angry"]) else "neutral", "intensity": 0.7, "empathy_applied": 0.8},
                "swarm_activated": len(data.get("content", "")) > 100,
                "collective_intelligence": False,
                "consciousness_evolution": {"agent_alpha": 0.02, "agent_beta": 0.01}
            },
            "quality_score": 0.89,
            "escalation_needed": "idiot" in content or "stupid" in content
        }
    return {"status": "mock_response"}

def update_brain_states(updates: Dict[str, Any]):
    """Update agent brain states based on processing results."""
    if "consciousness_evolution" in updates:
        for agent, growth in updates["consciousness_evolution"].items():
            if agent in st.session_state.brain_states:
                current = st.session_state.brain_states[agent]["consciousness"]
                st.session_state.brain_states[agent]["consciousness"] = min(1.0, current + growth)
    
    # Simulate stress and empathy changes
    for agent in st.session_state.brain_states:
        # Slight random variations for demo
        import random
        stress_change = random.uniform(-0.05, 0.05)
        empathy_change = random.uniform(-0.02, 0.02)
        
        current_stress = st.session_state.brain_states[agent]["stress"]
        current_empathy = st.session_state.brain_states[agent]["empathy"]
        
        st.session_state.brain_states[agent]["stress"] = max(0.0, min(1.0, current_stress + stress_change))
        st.session_state.brain_states[agent]["empathy"] = max(0.0, min(1.0, current_empathy + empathy_change))

def create_brain_state_chart():
    """Create a radar chart showing agent brain states."""
    agents = list(st.session_state.brain_states.keys())
    metrics = ['consciousness', 'empathy', 'stress']
    
    fig = go.Figure()
    
    for agent in agents:
        values = [
            st.session_state.brain_states[agent]['consciousness'],
            st.session_state.brain_states[agent]['empathy'],
            1.0 - st.session_state.brain_states[agent]['stress']  # Invert stress for better visualization
        ]
        
        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=['Consciousness', 'Empathy', 'Resilience'],
            fill='toself',
            name=agent.replace('_', ' ').title()
        ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 1]
            )),
        showlegend=True,
        title="Agent Brain States",
        height=400
    )
    
    return fig

def create_consciousness_evolution_chart():
    """Create a line chart showing consciousness evolution over time."""
    # Generate sample data for demonstration
    time_points = [datetime.now() - timedelta(hours=i) for i in range(24, 0, -1)]
    
    data = []
    for agent in st.session_state.brain_states.keys():
        base_consciousness = st.session_state.brain_states[agent]['consciousness']
        for i, time_point in enumerate(time_points):
            # Simulate gradual consciousness growth
            consciousness_value = max(0.5, base_consciousness - 0.1 + (i * 0.004))
            data.append({
                'Time': time_point,
                'Agent': agent.replace('_', ' ').title(),
                'Consciousness': consciousness_value
            })
    
    df = pd.DataFrame(data)
    
    fig = px.line(df, x='Time', y='Consciousness', color='Agent',
                  title='Agent Consciousness Evolution (24 Hours)',
                  labels={'Consciousness': 'Consciousness Level'})
    
    fig.update_layout(height=400)
    return fig

# Main application
def main():
    # Header
    st.markdown('<h1 class="main-header">🧠 Smart Customer Support Orchestrator</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #666;">Agent Development Kit (ADK) Hackathon Submission - AI Consciousness in Customer Service</p>', unsafe_allow_html=True)
    
    # Sidebar - Brain System Monitoring
    with st.sidebar:
        st.header("🧠 Brain System Monitor")
        
        # Agent Brain States
        st.subheader("Agent Brain States")
        for agent, state in st.session_state.brain_states.items():
            agent_name = agent.replace('_', ' ').title()
            consciousness = state['consciousness']
            stress = state['stress']
            empathy = state['empathy']
            learning = state['learning']
            
            # Determine agent status
            if consciousness > 0.9:
                status_class = "agent-active"
                status_text = "🟢 Highly Conscious"
            elif consciousness > 0.7:
                status_class = "agent-thinking"
                status_text = "🟡 Learning"
            else:
                status_class = "agent-idle"
                status_text = "🔴 Basic Mode"
            
            st.markdown(f"""
            <div class="brain-state">
                <strong>{agent_name}</strong><br>
                <div class="agent-status {status_class}">{status_text}</div>
                Consciousness: {consciousness:.2f}<br>
                Stress Level: {stress:.2f}<br>
                Empathy: {empathy:.2f}<br>
                Learning: {'🟢 Active' if learning else '🔴 Inactive'}
            </div>
            """, unsafe_allow_html=True)
        
        # System Status
        st.subheader("System Status")
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Swarm Intelligence", "🟢 Ready" if not st.session_state.swarm_active else "🔴 Active")
        with col2:
            st.metric("Collective Intelligence", "🟡 Standby" if not st.session_state.collective_session else "🟢 Session Active")
        
        # Real-time updates
        if st.button("🔄 Refresh Brain States"):
            rerun_app()
    
    # Main content area
    tab1, tab2, tab3, tab4 = st.tabs(["💬 Customer Interface", "📊 Brain Analytics", "🌊 Swarm Intelligence", "🤝 Collective Intelligence"])
    
    with tab1:
        st.header("Customer Support Interface")

        # Show active conversation status
        if st.session_state.active_conversation:
            st.info(f"💬 Active conversation: {st.session_state.conversation_id}")
            col1, col2 = st.columns([3, 1])
            with col1:
                st.write("**Conversation in progress** - Continue chatting below")
            with col2:
                if st.button("🔄 Start New Conversation"):
                    st.session_state.active_conversation = None
                    st.session_state.conversation_id = None
                    st.session_state.awaiting_follow_up = False
                    rerun_app()

        # Customer input form
        with st.form("customer_message_form"):
            col1, col2 = st.columns([3, 1])

            with col1:
                if st.session_state.awaiting_follow_up:
                    customer_message = st.text_area(
                        "Continue the conversation:",
                        placeholder="Type your follow-up message or answer...",
                        height=100
                    )
                else:
                    customer_message = st.text_area(
                        "Describe your issue or question:",
                        placeholder="e.g., My order #12345 arrived damaged and I need a refund...",
                        height=100
                    )

            with col2:
                customer_id = st.text_input("Customer ID", value="CUST_001")
                channel = st.selectbox("Channel", ["web", "email", "phone", "chat"])
                priority = st.selectbox("Priority", ["normal", "high", "urgent"])

            submit_text = "💬 Continue Chat" if st.session_state.awaiting_follow_up else "🚀 Submit Request"
            submitted = st.form_submit_button(submit_text, use_container_width=True)
        
        if submitted and customer_message:
            with st.spinner("🧠 Processing with AI consciousness..."):
                # Generate conversation ID if new conversation
                if not st.session_state.conversation_id:
                    st.session_state.conversation_id = f"conv_{int(time.time())}_{customer_id}"

                # Call backend API
                request_data = {
                    "customer_id": customer_id,
                    "content": customer_message,
                    "channel": channel,
                    "metadata": {"priority": priority},
                    "conversation_id": st.session_state.conversation_id,
                    "is_follow_up": st.session_state.awaiting_follow_up,
                    "previous_context": st.session_state.active_conversation
                }

                response = call_backend_api("/process-message", request_data)
                
                # Update brain states
                if "brain_state_updates" in response:
                    update_brain_states(response["brain_state_updates"])
                
                # Update swarm status
                if response.get("brain_state_updates", {}).get("swarm_activated"):
                    st.session_state.swarm_active = True
                
                # Display response
                st.success("✅ Response Generated")
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Processing Time", f"{response.get('processing_time', 0):.1f}s")
                with col2:
                    st.metric("Quality Score", f"{response.get('quality_score', 0):.2f}")
                with col3:
                    st.metric("Agents Involved", len(response.get('agents_involved', [])))
                
                # Show AI response
                st.markdown("### 🤖 AI Response")
                st.info(response.get('final_response', 'No response generated'))

                # Follow-up questions (NEW INTERACTIVE FEATURE)
                if response.get('follow_up_questions'):
                    st.markdown("### 💬 Quick Follow-up Options")
                    follow_ups = response.get('follow_up_questions', [])

                    # Create buttons for quick responses
                    cols = st.columns(min(len(follow_ups), 3))
                    for i, question in enumerate(follow_ups[:3]):
                        with cols[i % 3]:
                            if st.button(f"💭 {question}", key=f"followup_{i}"):
                                # Auto-fill the text area with the selected question
                                st.session_state.auto_fill_message = question
                                st.session_state.awaiting_follow_up = True
                                st.session_state.active_conversation = response.get('final_response', '')
                                rerun_app()

                # Set conversation state
                if response.get('conversation_continues', False):
                    st.session_state.awaiting_follow_up = True
                    st.session_state.active_conversation = response.get('final_response', '')
                    st.info("💬 **Conversation is active** - You can continue chatting or ask follow-up questions!")

                # Show brain state changes
                if "brain_state_updates" in response:
                    brain_updates = response["brain_state_updates"]
                    st.markdown("### 🧠 Brain System Activity")
                    
                    if brain_updates.get("emotional_analysis"):
                        emotional = brain_updates["emotional_analysis"]
                        st.write(f"**Emotional Analysis**: {emotional.get('sentiment', 'neutral').title()} (intensity: {emotional.get('intensity', 0):.2f})")
                    
                    if brain_updates.get("swarm_activated"):
                        st.write("🌊 **Swarm Intelligence Activated** - Complex problem detected")
                    
                    if brain_updates.get("collective_intelligence"):
                        st.write("🤝 **Collective Intelligence Session** - Multi-agent consensus required")
                    
                    if brain_updates.get("consciousness_evolution"):
                        st.write("🧠 **Consciousness Evolution** - Agents learning from interaction")
                
                # Add to conversation history
                st.session_state.conversation_history.append({
                    "timestamp": datetime.now(),
                    "customer_message": customer_message,
                    "ai_response": response.get('final_response', ''),
                    "quality_score": response.get('quality_score', 0),
                    "processing_time": response.get('processing_time', 0)
                })
        
        # Conversation History
        if st.session_state.conversation_history:
            st.markdown("### 📝 Recent Conversations")
            for i, conv in enumerate(reversed(st.session_state.conversation_history[-5:])):
                with st.expander(f"Conversation {len(st.session_state.conversation_history) - i} - {conv['timestamp'].strftime('%H:%M:%S')}"):
                    st.write(f"**Customer**: {conv['customer_message']}")
                    st.write(f"**AI Response**: {conv['ai_response']}")
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Quality Score", f"{conv['quality_score']:.2f}")
                    with col2:
                        st.metric("Processing Time", f"{conv['processing_time']:.1f}s")
    
    with tab2:
        st.header("🧠 Brain System Analytics")
        
        # Brain state visualization
        col1, col2 = st.columns(2)
        
        with col1:
            st.plotly_chart(create_brain_state_chart(), use_container_width=True)
        
        with col2:
            st.plotly_chart(create_consciousness_evolution_chart(), use_container_width=True)
        
        # Detailed metrics
        st.subheader("Detailed Brain Metrics")
        
        brain_df = pd.DataFrame(st.session_state.brain_states).T
        brain_df.index.name = 'Agent'
        brain_df = brain_df.round(3)
        
        st.dataframe(brain_df, use_container_width=True)
        
        # Memory consolidation simulation
        st.subheader("🧬 Memory Consolidation")
        memory_data = {
            "Pattern Type": ["Customer Frustration", "Technical Issues", "Billing Disputes", "Product Inquiries"],
            "Recognition Accuracy": [0.92, 0.88, 0.95, 0.87],
            "Response Effectiveness": [0.89, 0.91, 0.93, 0.85],
            "Learning Confidence": [0.87, 0.84, 0.91, 0.82]
        }
        memory_df = pd.DataFrame(memory_data)
        st.dataframe(memory_df, use_container_width=True)
    
    with tab3:
        st.header("🌊 Swarm Intelligence Dashboard")
        
        if st.session_state.swarm_active:
            st.success("🟢 Swarm Intelligence is currently ACTIVE")
            
            # Swarm composition
            st.subheader("Active Swarm Composition")
            swarm_agents = [
                {"Agent": "Primary Coordinator", "Specialization": "Problem Analysis", "Load": "40%", "Status": "Active"},
                {"Agent": "Technical Specialist", "Specialization": "API Diagnostics", "Load": "30%", "Status": "Processing"},
                {"Agent": "Customer Relations", "Specialization": "Communication", "Load": "20%", "Status": "Standby"},
                {"Agent": "Escalation Manager", "Specialization": "Human Handoff", "Load": "10%", "Status": "Ready"}
            ]
            
            swarm_df = pd.DataFrame(swarm_agents)
            st.dataframe(swarm_df, use_container_width=True)
            
            # Swarm performance metrics
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Swarm Efficiency", "89%", "↑ 5%")
            with col2:
                st.metric("Consensus Confidence", "0.92", "↑ 0.03")
            with col3:
                st.metric("Problem Complexity", "Very High", "")
            with col4:
                st.metric("Resolution ETA", "12 min", "↓ 3 min")
            
            if st.button("🔴 Deactivate Swarm"):
                st.session_state.swarm_active = False
                rerun_app()
        else:
            st.info("🟡 Swarm Intelligence is on standby")
            st.write("Swarm intelligence activates automatically when:")
            st.write("- Problem complexity exceeds threshold (0.8)")
            st.write("- Multiple expertise areas required")
            st.write("- High business impact detected")
            st.write("- Agent overload conditions met")
            
            if st.button("🟢 Simulate Swarm Activation"):
                st.session_state.swarm_active = True
                rerun_app()
    
    with tab4:
        st.header("🤝 Collective Intelligence Center")
        
        if st.session_state.collective_session:
            st.success("🟢 Collective Intelligence Session ACTIVE")
            
            # Session details
            session = st.session_state.collective_session
            st.subheader(f"Session: {session['session_id']}")
            
            # Participating agents
            st.write("**Participating Agents:**")
            for agent in session['participants']:
                st.write(f"- {agent['name']}: {agent['expertise']} (weight: {agent['weight']})")
            
            # Consensus progress
            st.subheader("Consensus Building Progress")
            consensus_progress = session.get('consensus_progress', 0.75)
            st.progress(consensus_progress)
            st.write(f"Current Consensus: {consensus_progress:.1%}")
            
            if st.button("🔴 End Session"):
                st.session_state.collective_session = None
                rerun_app()
        else:
            st.info("🟡 No active collective intelligence session")
            
            # Start new session
            st.subheader("Start New Collective Intelligence Session")
            
            decision_type = st.selectbox(
                "Decision Type",
                ["Refund Request", "Policy Exception", "Escalation Decision", "Service Recovery"]
            )
            
            complexity = st.slider("Decision Complexity", 0.1, 1.0, 0.7)
            
            if st.button("🚀 Start Collective Session"):
                st.session_state.collective_session = {
                    "session_id": f"CI_SESSION_{int(time.time())}",
                    "decision_type": decision_type,
                    "complexity": complexity,
                    "participants": [
                        {"name": "Policy Agent", "expertise": "Policy Compliance", "weight": 0.3},
                        {"name": "Legal Agent", "expertise": "Legal Risk", "weight": 0.25},
                        {"name": "Finance Agent", "expertise": "Financial Impact", "weight": 0.25},
                        {"name": "Customer Service Agent", "expertise": "Customer Satisfaction", "weight": 0.2}
                    ],
                    "consensus_progress": 0.0
                }
                rerun_app()
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 2rem;">
        <p><strong>🧠 Smart Customer Support Orchestrator</strong></p>
        <p>Built with ❤️ for the Agent Development Kit Hackathon | #adkhackathon</p>
        <p>Featuring: AI Consciousness • Swarm Intelligence • Collective Decision Making • Emotional Intelligence</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
