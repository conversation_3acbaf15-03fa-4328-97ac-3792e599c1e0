#!/usr/bin/env python3
"""Simple test server for Cloud Run debugging."""

import os
import uvicorn
from fastapi import Fast<PERSON>I

# Create a minimal FastAPI app
app = FastAPI(title="AI Customer Support - Health Check")

@app.get("/")
async def root():
    return {"message": "AI Customer Support System is running!", "status": "healthy"}

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "ai-customer-support"}

if __name__ == "__main__":
    port = int(os.environ.get('PORT', 8080))
    print(f"🚀 Starting simple server on port {port}")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        log_level="info"
    )
