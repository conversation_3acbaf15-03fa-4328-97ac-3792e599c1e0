#!/usr/bin/env python3
"""Test script for outbound calling system."""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta

API_BASE = "http://localhost:8080"

async def test_outbound_calling():
    """Test the outbound calling system."""
    print("🤖 Testing AI Outbound Calling System")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        
        # Test 1: Schedule a single outbound call
        print("\n📞 Test 1: Scheduling a single outbound call")
        call_data = {
            "customer_id": "CUST_001",
            "purpose": "follow_up",
            "priority": 5,
            "metadata": {
                "test": True,
                "reason": "Testing outbound call system"
            }
        }
        
        try:
            async with session.post(f"{API_BASE}/outbound/schedule-call", json=call_data) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Call scheduled successfully: {result['call_id']}")
                    call_id = result['call_id']
                else:
                    print(f"❌ Failed to schedule call: {response.status}")
                    return
        except Exception as e:
            print(f"❌ Error scheduling call: {e}")
            return
        
        # Test 2: Check call status
        print(f"\n📋 Test 2: Checking call status for {call_id}")
        try:
            async with session.get(f"{API_BASE}/outbound/call/{call_id}") as response:
                if response.status == 200:
                    result = await response.json()
                    call_info = result['call']
                    print(f"✅ Call status: {call_info['status']}")
                    print(f"   Customer: {call_info['customer_id']}")
                    print(f"   Purpose: {call_info['purpose']}")
                    print(f"   Scheduled: {call_info['scheduled_time']}")
                else:
                    print(f"❌ Failed to get call status: {response.status}")
        except Exception as e:
            print(f"❌ Error getting call status: {e}")
        
        # Test 3: Get call queue
        print("\n📋 Test 3: Getting call queue")
        try:
            async with session.get(f"{API_BASE}/outbound/queue") as response:
                if response.status == 200:
                    result = await response.json()
                    queue = result['queue']
                    print(f"✅ Call queue has {len(queue)} calls")
                    for call in queue[:3]:  # Show first 3 calls
                        print(f"   - {call['customer_id']}: {call['purpose']} ({call['status']})")
                else:
                    print(f"❌ Failed to get call queue: {response.status}")
        except Exception as e:
            print(f"❌ Error getting call queue: {e}")
        
        # Test 4: Create a campaign
        print("\n🎯 Test 4: Creating a call campaign")
        campaign_data = {
            "name": "Test Follow-up Campaign",
            "campaign_type": "follow_up",
            "purpose": "follow_up",
            "target_criteria": {
                "customer_type": ["premium", "vip"]
            },
            "start_time": (datetime.now() + timedelta(minutes=5)).isoformat(),
            "max_calls_per_hour": 5,
            "business_hours_only": True,
            "metadata": {
                "test": True,
                "description": "Test campaign for outbound calling system"
            }
        }
        
        try:
            async with session.post(f"{API_BASE}/campaigns", json=campaign_data) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Campaign created successfully: {result['campaign_id']}")
                    campaign_id = result['campaign_id']
                else:
                    print(f"❌ Failed to create campaign: {response.status}")
                    text = await response.text()
                    print(f"   Error: {text}")
                    return
        except Exception as e:
            print(f"❌ Error creating campaign: {e}")
            return
        
        # Test 5: Start the campaign
        print(f"\n▶️ Test 5: Starting campaign {campaign_id}")
        try:
            async with session.post(f"{API_BASE}/campaigns/{campaign_id}/start") as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Campaign started: {result['message']}")
                else:
                    print(f"❌ Failed to start campaign: {response.status}")
        except Exception as e:
            print(f"❌ Error starting campaign: {e}")
        
        # Test 6: List campaigns
        print("\n📋 Test 6: Listing campaigns")
        try:
            async with session.get(f"{API_BASE}/campaigns") as response:
                if response.status == 200:
                    result = await response.json()
                    campaigns = result['campaigns']
                    print(f"✅ Found {len(campaigns)} campaigns")
                    for campaign in campaigns:
                        print(f"   - {campaign['name']}: {campaign['status']} ({campaign['total_calls_scheduled']} calls)")
                else:
                    print(f"❌ Failed to list campaigns: {response.status}")
        except Exception as e:
            print(f"❌ Error listing campaigns: {e}")
        
        # Test 7: Wait a bit and check active calls
        print("\n⏳ Test 7: Waiting 10 seconds and checking active calls...")
        await asyncio.sleep(10)
        
        try:
            async with session.get(f"{API_BASE}/outbound/active") as response:
                if response.status == 200:
                    result = await response.json()
                    active_calls = result['active_calls']
                    print(f"✅ Found {len(active_calls)} active calls")
                    for call in active_calls:
                        print(f"   - {call['customer_id']}: {call['status']}")
                else:
                    print(f"❌ Failed to get active calls: {response.status}")
        except Exception as e:
            print(f"❌ Error getting active calls: {e}")
        
        # Test 8: Pause the campaign
        print(f"\n⏸️ Test 8: Pausing campaign {campaign_id}")
        try:
            async with session.post(f"{API_BASE}/campaigns/{campaign_id}/pause") as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Campaign paused: {result['message']}")
                else:
                    print(f"❌ Failed to pause campaign: {response.status}")
        except Exception as e:
            print(f"❌ Error pausing campaign: {e}")
        
        print("\n🎉 Outbound calling system test completed!")
        print("\nNext steps:")
        print("1. Open the dashboard at: file:///home/<USER>/aiagent/outbound_call_dashboard.html")
        print("2. Monitor calls and campaigns in real-time")
        print("3. Integrate with Twilio for real phone calls")
        print("4. Set up webhooks for call events")


async def test_conversation_flows():
    """Test conversation flow generation."""
    print("\n🗣️ Testing AI Conversation Flows")
    print("=" * 30)
    
    # Import conversation flow manager
    import sys
    sys.path.append('/home/<USER>/aiagent')
    
    from src.services.conversation_flows import conversation_flow_manager, ConversationStage
    
    # Test different conversation flows
    purposes = ["follow_up", "survey", "proactive_support", "retention"]
    customer_id = "CUST_001"
    
    for purpose in purposes:
        print(f"\n📋 Testing {purpose} conversation flow:")
        
        # Get greeting script
        greeting = await conversation_flow_manager.get_conversation_script(
            purpose=purpose,
            customer_id=customer_id,
            stage=ConversationStage.GREETING
        )
        print(f"   Greeting: {greeting}")
        
        # Get purpose explanation
        explanation = await conversation_flow_manager.get_conversation_script(
            purpose=purpose,
            customer_id=customer_id,
            stage=ConversationStage.PURPOSE_EXPLANATION
        )
        print(f"   Purpose: {explanation}")
        
        # Get expected duration
        duration = conversation_flow_manager.get_expected_duration(purpose)
        print(f"   Expected duration: {duration} minutes")


if __name__ == "__main__":
    print("🚀 Starting Outbound Calling System Tests")
    
    # Run the tests
    asyncio.run(test_outbound_calling())
    asyncio.run(test_conversation_flows())
