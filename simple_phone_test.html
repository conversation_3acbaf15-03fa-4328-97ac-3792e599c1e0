<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📞 Simple Phone Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin: 0;
        }

        .phone-container {
            background: #000;
            border-radius: 30px;
            padding: 20px;
            width: 350px;
            height: 600px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .phone-screen {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
        }

        .caller-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            margin-bottom: 20px;
            border: 4px solid rgba(255,255,255,0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .caller-info h2 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .caller-info p {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .call-actions {
            margin-top: 40px;
            display: flex;
            gap: 40px;
        }

        .call-btn {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .answer-btn {
            background: #4CAF50;
            color: white;
        }

        .answer-btn:hover {
            background: #45a049;
            transform: scale(1.1);
        }

        .decline-btn {
            background: #f44336;
            color: white;
        }

        .decline-btn:hover {
            background: #da190b;
            transform: scale(1.1);
        }

        .hidden {
            display: none !important;
        }

        .active-call {
            text-align: center;
        }

        .status {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .test-info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="phone-screen">
            <!-- Incoming Call Screen -->
            <div id="incomingCallScreen">
                <div class="test-info">
                    <strong>📞 Phone Call Test</strong><br>
                    You are Max (Premium Customer)<br>
                    Angela is calling you for support
                </div>
                
                <div class="caller-avatar">👩‍💼</div>
                <div class="caller-info">
                    <h2>Customer Support</h2>
                    <p>Angela from Support</p>
                    <p>📞 Incoming Call</p>
                </div>
                <div class="call-actions">
                    <button class="call-btn decline-btn" onclick="testDecline()">❌</button>
                    <button class="call-btn answer-btn" onclick="testAnswer()">✅</button>
                </div>
            </div>

            <!-- Active Call Screen -->
            <div id="activeCallScreen" class="hidden">
                <div class="active-call">
                    <div class="caller-avatar">👩‍💼</div>
                    <h2>Angela</h2>
                    <p>Customer Support Agent</p>
                    
                    <div class="status" id="callStatus">
                        Call connected! Testing basic functionality...
                    </div>
                    
                    <div class="test-info">
                        <strong>✅ Success!</strong><br>
                        The answer button is working correctly.<br>
                        Angela would now introduce herself:<br><br>
                        <em>"Hi Max! I'm Angela from customer support. Am I speaking to Max?"</em>
                    </div>
                    
                    <div class="call-actions">
                        <button class="call-btn decline-btn" onclick="testEndCall()">📞 End</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testAnswer() {
            console.log('Answer button clicked!');
            alert('✅ Answer button works! Switching to call screen...');
            
            // Hide incoming screen
            document.getElementById('incomingCallScreen').classList.add('hidden');
            
            // Show active call screen
            document.getElementById('activeCallScreen').classList.remove('hidden');
            
            console.log('Screen switched successfully');
        }

        function testDecline() {
            console.log('Decline button clicked!');
            alert('❌ Call declined! This button works too.');
            
            document.body.innerHTML = `
                <div style="text-align: center; color: white; font-size: 2rem; margin-top: 200px;">
                    📞 Call Declined
                    <br><br>
                    <button onclick="location.reload()" style="padding: 15px 30px; font-size: 1rem; border: none; border-radius: 25px; background: #4CAF50; color: white; cursor: pointer;">
                        📞 Try Again
                    </button>
                </div>
            `;
        }

        function testEndCall() {
            console.log('End call button clicked!');
            alert('📞 Call ended! All buttons are working correctly.');
            
            document.body.innerHTML = `
                <div style="text-align: center; color: white; font-size: 2rem; margin-top: 200px;">
                    📞 Call Ended
                    <br><br>
                    <p style="font-size: 1rem; margin: 20px 0;">
                        ✅ All buttons are working correctly!<br>
                        The full phone simulation should work now.
                    </p>
                    <button onclick="location.reload()" style="padding: 15px 30px; font-size: 1rem; border: none; border-radius: 25px; background: #4CAF50; color: white; cursor: pointer;">
                        📞 Test Again
                    </button>
                </div>
            `;
        }

        // Test on page load
        window.addEventListener('load', () => {
            console.log('Simple phone test loaded');
            console.log('Incoming screen:', document.getElementById('incomingCallScreen'));
            console.log('Active screen:', document.getElementById('activeCallScreen'));
        });
    </script>
</body>
</html>
