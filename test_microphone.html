<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 Microphone Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .mic-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 25px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .mic-btn:hover {
            background: #1976D2;
        }
        .mic-btn.recording {
            background: #f44336;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        .status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .recording {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <h1>🎤 Microphone Test for Phone Simulation</h1>
    
    <div class="test-section">
        <h2>1. Microphone Access Test</h2>
        <button onclick="testMicrophoneAccess()" class="mic-btn">🎤 Test Microphone Access</button>
        <div id="micAccessResult" class="status" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Hold-to-Speak Test</h2>
        <p>Hold down the button and speak, then release:</p>
        <button id="holdToSpeakBtn" class="mic-btn">🎤 Hold to Speak</button>
        <div id="holdToSpeakResult" class="status" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Voice Processing Test</h2>
        <p>This will test the full voice pipeline (requires server running):</p>
        <button id="voiceProcessBtn" class="mic-btn">🎤 Test Voice Processing</button>
        <div id="voiceProcessResult" class="status" style="display: none;"></div>
        <audio id="responseAudio" controls style="width: 100%; margin-top: 10px; display: none;"></audio>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';
        let mediaRecorder;
        let audioChunks = [];
        let isRecording = false;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `status ${type}`;
            element.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            console.log(message);
        }

        async function testMicrophoneAccess() {
            try {
                log('micAccessResult', 'Requesting microphone access...', 'info');
                
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                log('micAccessResult', '✅ Microphone access granted!', 'success');
                log('micAccessResult', 'Stream active: ' + stream.active, 'success');
                
                // Stop the stream
                stream.getTracks().forEach(track => track.stop());
                log('micAccessResult', 'Microphone test completed successfully.', 'success');
                
            } catch (error) {
                log('micAccessResult', `❌ Microphone access failed: ${error.message}`, 'error');
                log('micAccessResult', 'Please allow microphone access in your browser.', 'error');
            }
        }

        async function initializeMicrophone() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                
                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                    }
                };
                
                mediaRecorder.onstop = () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    audioChunks = [];
                    log('holdToSpeakResult', `✅ Recording completed! Audio size: ${audioBlob.size} bytes`, 'success');
                };
                
                return true;
            } catch (error) {
                log('holdToSpeakResult', `❌ Failed to initialize microphone: ${error.message}`, 'error');
                return false;
            }
        }

        async function startRecording() {
            if (isRecording) return;
            
            if (!mediaRecorder) {
                const micInitialized = await initializeMicrophone();
                if (!micInitialized) return;
            }
            
            isRecording = true;
            audioChunks = [];
            
            log('holdToSpeakResult', '🔴 Recording started... Speak now!', 'recording');
            const btn = document.getElementById('holdToSpeakBtn');
            btn.textContent = '🔴 Recording...';
            btn.classList.add('recording');
            
            mediaRecorder.start();
        }

        function stopRecording() {
            if (!isRecording || !mediaRecorder) return;
            
            isRecording = false;
            log('holdToSpeakResult', '⏹️ Recording stopped, processing...', 'info');
            const btn = document.getElementById('holdToSpeakBtn');
            btn.textContent = '🎤 Hold to Speak';
            btn.classList.remove('recording');
            
            mediaRecorder.stop();
        }

        async function testVoiceProcessing() {
            try {
                if (!mediaRecorder) {
                    const micInitialized = await initializeMicrophone();
                    if (!micInitialized) return;
                }
                
                log('voiceProcessResult', '🎤 Starting voice processing test...', 'info');
                log('voiceProcessResult', 'Hold the button and say something like "Hello Angela"', 'info');
                
                // Set up one-time recording for voice processing
                mediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    audioChunks = [];
                    
                    log('voiceProcessResult', `📤 Sending audio to server (${audioBlob.size} bytes)...`, 'info');
                    
                    try {
                        const formData = new FormData();
                        formData.append('audio', audioBlob, 'test_voice.wav');
                        formData.append('customer_id', 'CUST_001');
                        
                        const response = await fetch(`${API_BASE}/voice/process-voice-message`, {
                            method: 'POST',
                            body: formData
                        });
                        
                        if (response.ok) {
                            const result = await response.json();
                            log('voiceProcessResult', `✅ Voice processing successful!`, 'success');
                            log('voiceProcessResult', `📝 Transcript: "${result.transcript}"`, 'success');
                            log('voiceProcessResult', `🤖 AI Response: "${result.ai_response}"`, 'success');
                            
                            // Play audio response if available
                            if (result.audio_response) {
                                log('voiceProcessResult', `🔊 Playing Angela's voice response...`, 'success');
                                const audioData = new Uint8Array(result.audio_response.match(/.{1,2}/g).map(byte => parseInt(byte, 16)));
                                const responseBlob = new Blob([audioData], { type: 'audio/mpeg' });
                                const audioUrl = URL.createObjectURL(responseBlob);
                                const audioElement = document.getElementById('responseAudio');
                                audioElement.src = audioUrl;
                                audioElement.style.display = 'block';
                                audioElement.play();
                            }
                        } else {
                            throw new Error(`Server error: ${response.status}`);
                        }
                    } catch (error) {
                        log('voiceProcessResult', `❌ Voice processing failed: ${error.message}`, 'error');
                    }
                };
                
            } catch (error) {
                log('voiceProcessResult', `❌ Test setup failed: ${error.message}`, 'error');
            }
        }

        // Initialize event listeners
        window.addEventListener('load', () => {
            // Hold-to-speak button
            const holdBtn = document.getElementById('holdToSpeakBtn');
            holdBtn.addEventListener('mousedown', startRecording);
            holdBtn.addEventListener('mouseup', stopRecording);
            holdBtn.addEventListener('mouseleave', stopRecording);
            
            // Touch events for mobile
            holdBtn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                startRecording();
            });
            holdBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                stopRecording();
            });
            
            // Voice processing test button
            const voiceBtn = document.getElementById('voiceProcessBtn');
            voiceBtn.addEventListener('click', testVoiceProcessing);
            voiceBtn.addEventListener('mousedown', startRecording);
            voiceBtn.addEventListener('mouseup', stopRecording);
            voiceBtn.addEventListener('mouseleave', stopRecording);
            
            console.log('Microphone test page loaded');
        });
    </script>
</body>
</html>
