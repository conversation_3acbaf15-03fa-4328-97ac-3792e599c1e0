<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        input {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🎤 Voice API Test Interface</h1>
    
    <div class="test-section">
        <h2>1. Customer Lookup Test</h2>
        <input type="email" id="testEmail" placeholder="Enter email (try: <EMAIL>)" value="<EMAIL>">
        <button onclick="testCustomerLookup()">Test Customer Lookup</button>
        <div id="lookupResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Voice Introduction Test</h2>
        <input type="email" id="introEmail" placeholder="Enter email" value="<EMAIL>">
        <input type="text" id="introName" placeholder="Enter name (optional)">
        <button onclick="testVoiceIntroduction()">Test Voice Introduction</button>
        <div id="introResult" class="result" style="display: none;"></div>
        <audio id="introAudio" controls style="width: 100%; margin-top: 10px; display: none;"></audio>
    </div>
    
    <div class="test-section">
        <h2>3. Test Different Customers</h2>
        <button onclick="testCustomer('<EMAIL>', 'Max')">Test Max (Premium)</button>
        <button onclick="testCustomer('<EMAIL>', 'Sarah')">Test Sarah (Regular)</button>
        <button onclick="testCustomer('<EMAIL>', 'John')">Test John (VIP)</button>
        <button onclick="testCustomer('<EMAIL>', 'Unknown')">Test Unknown Customer</button>
        <div id="customerTestResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';

        async function testCustomerLookup() {
            const email = document.getElementById('testEmail').value;
            const resultDiv = document.getElementById('lookupResult');
            
            try {
                const response = await fetch(`${API_BASE}/voice/customer-lookup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_email: email
                    })
                });
                
                const result = await response.json();
                resultDiv.textContent = JSON.stringify(result, null, 2);
                resultDiv.className = response.ok ? 'result success' : 'result error';
                resultDiv.style.display = 'block';
                
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        async function testVoiceIntroduction() {
            const email = document.getElementById('introEmail').value;
            const name = document.getElementById('introName').value;
            const resultDiv = document.getElementById('introResult');
            const audioElement = document.getElementById('introAudio');
            
            try {
                const response = await fetch(`${API_BASE}/voice/introduction`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_email: email,
                        customer_name: name
                    })
                });
                
                if (response.ok) {
                    // Get headers
                    const angelaMessage = response.headers.get('X-Angela-Message');
                    const customerIdentified = response.headers.get('X-Customer-Identified');
                    const customerDetails = response.headers.get('X-Customer-Details');
                    
                    resultDiv.innerHTML = `
✅ Success!
Angela's Message: "${angelaMessage}"
Customer Identified: ${customerIdentified}
Customer Details: ${customerDetails}
Audio Size: ${response.headers.get('content-length')} bytes
                    `;
                    resultDiv.className = 'result success';
                    
                    // Play audio
                    const audioBlob = await response.blob();
                    const audioUrl = URL.createObjectURL(audioBlob);
                    audioElement.src = audioUrl;
                    audioElement.style.display = 'block';
                    
                } else {
                    resultDiv.textContent = `Error: ${response.status} ${response.statusText}`;
                    resultDiv.className = 'result error';
                }
                
                resultDiv.style.display = 'block';
                
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        async function testCustomer(email, name) {
            const resultDiv = document.getElementById('customerTestResult');
            
            try {
                // First lookup
                const lookupResponse = await fetch(`${API_BASE}/voice/customer-lookup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_email: email
                    })
                });
                
                const lookupResult = await lookupResponse.json();
                
                // Then introduction
                const introResponse = await fetch(`${API_BASE}/voice/introduction`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_email: email,
                        customer_name: name
                    })
                });
                
                const angelaMessage = introResponse.headers.get('X-Angela-Message');
                
                resultDiv.innerHTML = `
🧪 Testing: ${name} (${email})

📋 Lookup Result:
${JSON.stringify(lookupResult, null, 2)}

🎤 Angela's Introduction:
"${angelaMessage}"

✅ Both APIs working correctly!
                `;
                resultDiv.className = 'result success';
                
            } catch (error) {
                resultDiv.textContent = `Error testing ${name}: ${error.message}`;
                resultDiv.className = 'result error';
            }
            
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
