<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📞 Incoming Call - Customer Support</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .phone-container {
            background: #000;
            border-radius: 30px;
            padding: 20px;
            width: 350px;
            height: 600px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }

        .phone-screen {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .incoming-call-screen {
            text-align: center;
            animation: pulse 2s infinite;
        }

        .call-active-screen {
            display: none;
            flex-direction: column;
            height: 100%;
            padding: 20px;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .caller-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            margin-bottom: 20px;
            border: 4px solid rgba(255,255,255,0.3);
        }

        .caller-info h2 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .caller-info p {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .call-actions {
            position: absolute;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 60px;
            z-index: 1000;
        }

        .call-btn {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1001;
            font-weight: bold;
        }

        .answer-btn {
            background: #4CAF50;
            color: white;
        }

        .answer-btn:hover {
            background: #45a049;
            transform: scale(1.1);
        }

        .decline-btn {
            background: #f44336;
            color: white;
        }

        .decline-btn:hover {
            background: #da190b;
            transform: scale(1.1);
        }

        .conversation-area {
            flex: 1;
            overflow-y: auto;
            margin: 20px 0;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
        }

        .message {
            margin: 10px 0;
            padding: 12px 16px;
            border-radius: 15px;
            max-width: 85%;
            word-wrap: break-word;
        }

        .message.angela {
            background: rgba(255,255,255,0.9);
            color: #333;
            align-self: flex-start;
            border-bottom-left-radius: 5px;
        }

        .message.customer {
            background: #4CAF50;
            color: white;
            align-self: flex-end;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }

        .call-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 10px;
        }

        .control-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mic-btn {
            background: #2196F3;
            color: white;
        }

        .mic-btn:hover {
            background: #1976D2;
        }

        .mic-btn.recording {
            background: #f44336;
            animation: pulse 1s infinite;
        }

        .end-call-btn {
            background: #f44336;
            color: white;
        }

        .end-call-btn:hover {
            background: #da190b;
        }

        .status-indicator {
            text-align: center;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .customer-info-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            font-size: 0.8rem;
        }

        .customer-info-panel h4 {
            margin-bottom: 8px;
            color: #FFD700;
        }

        .speaking-indicator {
            display: none;
            position: absolute;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            animation: pulse 1s infinite;
        }

        .speaking-indicator.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="phone-screen">
            <!-- Incoming Call Screen -->
            <div class="incoming-call-screen" id="incomingCallScreen">
                <div class="caller-avatar">👩‍💼</div>
                <div class="caller-info">
                    <h2>Customer Support</h2>
                    <p>Angela from Support</p>
                    <p>📞 Incoming Call</p>
                </div>
                <div class="call-actions">
                    <button class="call-btn decline-btn" id="declineBtn">❌ Decline</button>
                    <button class="call-btn answer-btn" id="answerBtn">✅ Answer</button>
                </div>
            </div>

            <!-- Active Call Screen -->
            <div class="call-active-screen" id="activeCallScreen">
                <div class="speaking-indicator" id="speakingIndicator">🎤 Angela Speaking...</div>
                
                <div class="customer-info-panel">
                    <h4>📋 Your Profile (Max)</h4>
                    <div id="customerDetails">
                        Customer ID: CUST_001<br>
                        Type: Premium Customer<br>
                        Orders: 12 • Spent: R22,450.75<br>
                        Support Tier: Priority
                    </div>
                </div>

                <div class="status-indicator" id="callStatus">
                    Angela is introducing herself...
                </div>

                <div class="conversation-area" id="conversationArea">
                    <!-- Messages will appear here -->
                </div>

                <div class="call-controls">
                    <button class="control-btn mic-btn" id="micBtn">
                        🎤 Hold to Speak
                    </button>
                    <button class="control-btn end-call-btn" id="endCallBtn">
                        📞 End Call
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        const API_BASE = 'http://localhost:8000';
        let mediaRecorder;
        let audioChunks = [];
        let isRecording = false;
        let conversationHistory = [];
        
        // Max's customer data
        const customerData = {
            customer_id: "CUST_001",
            name: "Max",
            email: "<EMAIL>",
            customer_type: "premium",
            support_tier: "priority",
            total_orders: 12,
            total_spent: 22450.75,
            last_contact: "2024-01-20T14:22:00Z",
            preferred_channel: "email"
        };

        // Utility functions
        function updateStatus(message, type) {
            const statusElement = document.getElementById('callStatus');
            if (statusElement) {
                statusElement.textContent = message;
                statusElement.className = `status-indicator ${type}`;
            }
        }

        function addMessage(sender, message) {
            const conversation = document.getElementById('conversationArea');
            if (conversation) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                messageDiv.textContent = message;
                conversation.appendChild(messageDiv);
                conversation.scrollTop = conversation.scrollHeight;
                
                conversationHistory.push({ sender, message, timestamp: new Date() });
            }
        }

        // Answer the call
        async function answerCall() {
            console.log('Answer call button clicked!');

            try {
                // Hide incoming screen
                const incomingScreen = document.getElementById('incomingCallScreen');
                const activeScreen = document.getElementById('activeCallScreen');

                if (incomingScreen && activeScreen) {
                    incomingScreen.style.display = 'none';
                    activeScreen.style.display = 'flex';
                    console.log('Screens switched successfully');
                } else {
                    console.error('Could not find screen elements');
                    return;
                }

                // Start Angela's introduction
                updateStatus('Angela is introducing herself...', 'speaking');

                try {
                    // Get Angela's personalized introduction
                    const response = await fetch(`${API_BASE}/voice/introduction`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            customer_name: customerData.name,
                            customer_id: customerData.customer_id,
                            customer_email: customerData.email
                        })
                    });

                    if (response.ok) {
                        const audioBlob = await response.blob();
                        const audioUrl = URL.createObjectURL(audioBlob);
                        const audio = new Audio(audioUrl);

                        // Get Angela's message from headers
                        const angelaMessage = response.headers.get('X-Angela-Message') ||
                            'Hi Max! I am Angela from customer support. How can I help you today?';

                        // Show speaking indicator
                        document.getElementById('speakingIndicator').classList.add('active');

                        // Play Angela's introduction
                        audio.play();

                        // Add message to conversation
                        addMessage('angela', angelaMessage);

                        audio.onended = () => {
                            document.getElementById('speakingIndicator').classList.remove('active');
                            updateStatus('Listening... Hold the microphone button to speak', 'listening');
                        };
                    } else {
                        throw new Error('Failed to get introduction');
                    }
                } catch (error) {
                    console.error('Error playing introduction:', error);
                    updateStatus('Ready to help you', 'listening');
                    addMessage('angela', 'Hi Max! I am Angela from customer support. I can see you are a premium customer with 12 orders. How can I help you today?');
                }

            } catch (error) {
                console.error('Error in answerCall:', error);
                updateStatus('Error starting call', 'error');
            }
        }

        // Decline the call
        function declineCall() {
            console.log('Decline call button clicked!');
            document.body.innerHTML = '<div style="text-align: center; color: white; font-size: 2rem; margin-top: 200px;">📞 Call Declined<br><br><button onclick="location.reload()" style="padding: 15px 30px; font-size: 1rem; border: none; border-radius: 25px; background: #4CAF50; color: white; cursor: pointer;">📞 Simulate New Call</button></div>';
        }

        // Initialize microphone access
        async function initializeMicrophone() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    audioChunks = [];
                    processVoiceMessage(audioBlob);
                };

                return true;
            } catch (error) {
                console.error('Microphone access denied:', error);
                updateStatus('Microphone access required for voice chat', 'error');
                return false;
            }
        }

        // Start recording
        async function startRecording() {
            if (isRecording) return;

            if (!mediaRecorder) {
                const micInitialized = await initializeMicrophone();
                if (!micInitialized) return;
            }

            isRecording = true;
            audioChunks = [];

            updateStatus('Listening... Speak now', 'listening');
            const micBtn = document.getElementById('micBtn');
            micBtn.textContent = '🔴 Recording...';
            micBtn.classList.add('recording');

            mediaRecorder.start();
        }

        // Stop recording
        function stopRecording() {
            if (!isRecording || !mediaRecorder) return;

            isRecording = false;
            updateStatus('Processing your message...', 'processing');
            const micBtn = document.getElementById('micBtn');
            micBtn.textContent = '🎤 Hold to Speak';
            micBtn.classList.remove('recording');

            mediaRecorder.stop();
        }

        // Process voice message with customer context
        async function processVoiceMessage(audioBlob) {
            try {
                const formData = new FormData();
                formData.append('audio', audioBlob, 'voice_message.wav');
                formData.append('customer_id', customerData.customer_id);

                const response = await fetch(`${API_BASE}/voice/process-voice-message`, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();

                    // Add customer message to conversation
                    addMessage('customer', result.transcript);

                    // Add Angela's response to conversation
                    addMessage('angela', result.ai_response);

                    // Play Angela's voice response
                    if (result.audio_response) {
                        const audioData = new Uint8Array(result.audio_response.match(/.{1,2}/g).map(byte => parseInt(byte, 16)));
                        const audioBlob = new Blob([audioData], { type: 'audio/mpeg' });
                        const audioUrl = URL.createObjectURL(audioBlob);
                        const audio = new Audio(audioUrl);

                        updateStatus('Angela is responding...', 'speaking');
                        document.getElementById('speakingIndicator').classList.add('active');

                        audio.play();
                        audio.onended = () => {
                            document.getElementById('speakingIndicator').classList.remove('active');
                            updateStatus('Listening... Hold the microphone button to speak', 'listening');
                        };
                    } else {
                        updateStatus('Listening... Hold the microphone button to speak', 'listening');
                    }
                } else {
                    throw new Error('Failed to process voice message');
                }
            } catch (error) {
                console.error('Error processing voice message:', error);
                updateStatus('Error processing message. Please try again.', 'error');
                setTimeout(() => {
                    updateStatus('Listening... Hold the microphone button to speak', 'listening');
                }, 3000);
            }
        }

        // End call
        function endCall() {
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop();
            }

            document.body.innerHTML = '<div style="text-align: center; color: white; font-size: 2rem; margin-top: 200px;">📞 Call Ended<br><br><p style="font-size: 1rem; margin: 20px 0;">Thank you for using our customer support service, Max!</p><button onclick="location.reload()" style="padding: 15px 30px; font-size: 1rem; border: none; border-radius: 25px; background: #4CAF50; color: white; cursor: pointer;">📞 Simulate New Call</button></div>';
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            console.log('Phone call simulation loaded for customer Max (CUST_001)');

            // Add event listeners
            document.getElementById('answerBtn').addEventListener('click', answerCall);
            document.getElementById('declineBtn').addEventListener('click', declineCall);
            document.getElementById('endCallBtn').addEventListener('click', endCall);

            // Add microphone button event listeners (hold to speak)
            const micBtn = document.getElementById('micBtn');

            // Mouse events
            micBtn.addEventListener('mousedown', startRecording);
            micBtn.addEventListener('mouseup', stopRecording);
            micBtn.addEventListener('mouseleave', stopRecording); // Stop if mouse leaves button

            // Touch events for mobile
            micBtn.addEventListener('touchstart', (e) => {
                e.preventDefault();
                startRecording();
            });
            micBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                stopRecording();
            });

            console.log('Event listeners attached successfully');
        });
    </script>
</body>
</html>
