#!/bin/bash

# AI Customer Support System - Cloud Run Deployment Script
# This script deploys the AI customer support system to Google Cloud Run

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"your-project-id"}
REGION=${GOOGLE_CLOUD_REGION:-"us-central1"}
SERVICE_NAME="ai-customer-support"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo -e "${BLUE}🚀 AI Customer Support System - Cloud Run Deployment${NC}"
echo "=================================================="

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ Error: gcloud CLI is not installed${NC}"
    echo "Please install Google Cloud SDK: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${YELLOW}⚠️  Not authenticated with gcloud${NC}"
    echo "Running: gcloud auth login"
    gcloud auth login
fi

# Set project if not already set
if [ "$PROJECT_ID" = "your-project-id" ]; then
    echo -e "${YELLOW}⚠️  Please set your Google Cloud Project ID${NC}"
    read -p "Enter your Google Cloud Project ID: " PROJECT_ID
    export GOOGLE_CLOUD_PROJECT=$PROJECT_ID
fi

echo -e "${BLUE}📋 Configuration:${NC}"
echo "  Project ID: $PROJECT_ID"
echo "  Region: $REGION"
echo "  Service Name: $SERVICE_NAME"
echo "  Image: $IMAGE_NAME"
echo ""

# Set the project
echo -e "${BLUE}🔧 Setting up Google Cloud project...${NC}"
gcloud config set project $PROJECT_ID

# Enable required APIs
echo -e "${BLUE}🔌 Enabling required Google Cloud APIs...${NC}"
gcloud services enable \
    cloudbuild.googleapis.com \
    run.googleapis.com \
    containerregistry.googleapis.com \
    bigquery.googleapis.com \
    speech.googleapis.com \
    texttospeech.googleapis.com

# Build and push the container image
echo -e "${BLUE}🏗️  Building container image...${NC}"
gcloud builds submit --tag $IMAGE_NAME

# Deploy to Cloud Run
echo -e "${BLUE}🚀 Deploying to Cloud Run...${NC}"
gcloud run deploy $SERVICE_NAME \
    --image $IMAGE_NAME \
    --region $REGION \
    --platform managed \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 2 \
    --concurrency 100 \
    --max-instances 10 \
    --min-instances 0 \
    --timeout 300 \
    --set-env-vars "GOOGLE_CLOUD_PROJECT=${PROJECT_ID},BIGQUERY_DATASET=customer_support,GOOGLE_CLOUD_REGION=${REGION}" \
    --port 8080

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")

echo ""
echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo "=================================================="
echo -e "${GREEN}🌐 Service URL: ${SERVICE_URL}${NC}"
echo ""
echo -e "${BLUE}📋 Available endpoints:${NC}"
echo "  • Health Check: ${SERVICE_URL}/health"
echo "  • Process Message: ${SERVICE_URL}/process-message"
echo "  • Voice Processing: ${SERVICE_URL}/voice/process-voice-message"
echo "  • Outbound Calls: ${SERVICE_URL}/outbound/schedule-call"
echo "  • Campaigns: ${SERVICE_URL}/campaigns"
echo ""
echo -e "${BLUE}🧪 Test the deployment:${NC}"
echo "curl -X POST \"${SERVICE_URL}/process-message\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"customer_id\": \"CUST_001\", \"content\": \"my orders\", \"channel\": \"web\"}'"
echo ""
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "1. Set up BigQuery dataset and tables"
echo "2. Configure Twilio credentials for phone calls"
echo "3. Set up monitoring and logging"
echo "4. Configure custom domain (optional)"
echo ""
echo -e "${BLUE}🔧 Useful commands:${NC}"
echo "  • View logs: gcloud run services logs read $SERVICE_NAME --region=$REGION"
echo "  • Update service: gcloud run services update $SERVICE_NAME --region=$REGION"
echo "  • Delete service: gcloud run services delete $SERVICE_NAME --region=$REGION"
echo ""
echo -e "${GREEN}🎉 Your AI Customer Support System is now live!${NC}"
