<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📞 Phone Simulation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 1.1rem;
        }
        button:hover {
            background: #0056b3;
        }
        .phone-btn {
            background: #28a745;
            font-size: 1.5rem;
            padding: 20px 40px;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .customer-info {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>📞 Phone Call Simulation Test</h1>
    
    <div class="test-section">
        <h2>Customer Profile: Max (CUST_001)</h2>
        <div class="customer-info">
            <h3>📋 Max's Customer Information</h3>
            <div id="customerProfile">
                <strong>Customer ID:</strong> CUST_001<br>
                <strong>Name:</strong> Max<br>
                <strong>Email:</strong> <EMAIL><br>
                <strong>Type:</strong> Premium Customer<br>
                <strong>Support Tier:</strong> Priority<br>
                <strong>Total Orders:</strong> 12<br>
                <strong>Total Spent:</strong> R22,450.75<br>
                <strong>Last Contact:</strong> 2024-01-20<br>
                <strong>Preferred Channel:</strong> Email
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>1. Test Angela's Introduction for Max</h2>
        <button onclick="testIntroduction()">🎤 Test Angela's Introduction</button>
        <div id="introResult" class="result" style="display: none;"></div>
        <audio id="introAudio" controls style="width: 100%; margin-top: 10px; display: none;"></audio>
    </div>
    
    <div class="test-section">
        <h2>2. Launch Phone Call Simulation</h2>
        <p>This will open the phone call simulation where you can experience being called as Max and have a full conversation with Angela.</p>
        <button class="phone-btn" onclick="openPhoneSimulation()">📞 Start Phone Call as Max</button>
    </div>
    
    <div class="test-section">
        <h2>3. Test Customer Data Lookup</h2>
        <button onclick="testCustomerLookup()">🔍 Verify Max's Data</button>
        <div id="lookupResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Conversation Scenarios to Test</h2>
        <p>Once you're in the phone call, try asking Angela these questions:</p>
        <ul>
            <li><strong>"What's my order status?"</strong> - Angela should access Max's order history</li>
            <li><strong>"I need a refund for order 12345"</strong> - Test refund processing</li>
            <li><strong>"What's my total spending?"</strong> - Should mention R22,450.75</li>
            <li><strong>"Can you help me with my account?"</strong> - General account assistance</li>
            <li><strong>"I'm having trouble with my premium features"</strong> - Premium customer support</li>
        </ul>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';

        async function testIntroduction() {
            const resultDiv = document.getElementById('introResult');
            const audioElement = document.getElementById('introAudio');
            
            try {
                const response = await fetch(`${API_BASE}/voice/introduction`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_name: "Max",
                        customer_id: "CUST_001",
                        customer_email: "<EMAIL>"
                    })
                });
                
                if (response.ok) {
                    const angelaMessage = response.headers.get('X-Angela-Message');
                    const customerIdentified = response.headers.get('X-Customer-Identified');
                    const customerDetails = response.headers.get('X-Customer-Details');
                    
                    resultDiv.innerHTML = `
✅ Angela's Introduction Test Successful!

🎤 Angela says: "${angelaMessage}"
👤 Customer Identified: ${customerIdentified}
📊 Customer Details: ${customerDetails}
🔊 Audio Size: ${response.headers.get('content-length')} bytes

The introduction is working perfectly! Angela recognizes Max and uses his name.
                    `;
                    resultDiv.className = 'result success';
                    
                    // Play audio
                    const audioBlob = await response.blob();
                    const audioUrl = URL.createObjectURL(audioBlob);
                    audioElement.src = audioUrl;
                    audioElement.style.display = 'block';
                    
                } else {
                    resultDiv.textContent = `❌ Error: ${response.status} ${response.statusText}`;
                    resultDiv.className = 'result error';
                }
                
                resultDiv.style.display = 'block';
                
            } catch (error) {
                resultDiv.textContent = `❌ Error: ${error.message}`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        async function testCustomerLookup() {
            const resultDiv = document.getElementById('lookupResult');
            
            try {
                const response = await fetch(`${API_BASE}/voice/customer-lookup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        customer_email: "<EMAIL>"
                    })
                });
                
                const result = await response.json();
                
                if (result.found) {
                    resultDiv.innerHTML = `
✅ Customer Lookup Successful!

Max's data is correctly stored and accessible:
${JSON.stringify(result.customer, null, 2)}

Angela will have access to all this information during the call.
                    `;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `❌ Customer not found in database`;
                    resultDiv.className = 'result error';
                }
                
                resultDiv.style.display = 'block';
                
            } catch (error) {
                resultDiv.textContent = `❌ Error: ${error.message}`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        function openPhoneSimulation() {
            // Open the phone simulation in a new window
            const phoneWindow = window.open('phone_call_simulation.html', '_blank', 'width=400,height=700,resizable=yes,scrollbars=no');
            
            if (phoneWindow) {
                // Focus the new window
                phoneWindow.focus();
                
                // Show success message
                alert('📞 Phone simulation opened! You are now Max receiving a call from Angela.\n\nClick "Answer" to start the conversation.');
            } else {
                alert('❌ Please allow pop-ups to open the phone simulation.');
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            console.log('Phone simulation test page loaded');
        });
    </script>
</body>
</html>
