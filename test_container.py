#!/usr/bin/env python3
"""Test script to verify container startup issues."""

import os
import sys
import traceback

def test_imports():
    """Test all critical imports."""
    print("🔍 Testing imports...")
    
    try:
        print("  ✓ Testing basic imports...")
        import asyncio
        import uvicorn
        from fastapi import FastAPI
        print("  ✓ Basic imports successful")
        
        print("  ✓ Testing project imports...")
        from src.api.main import app
        print("  ✓ FastAPI app import successful")
        
        from src.config import settings
        print("  ✓ Settings import successful")
        
        from src.services.customer_database import customer_db
        print("  ✓ Customer database import successful")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_port_config():
    """Test port configuration."""
    print("\n🔍 Testing port configuration...")
    
    port = os.environ.get('PORT', '8080')
    print(f"  PORT environment variable: {port}")
    
    try:
        port_int = int(port)
        print(f"  ✓ Port {port_int} is valid")
        return port_int
    except ValueError:
        print(f"  ❌ Invalid port: {port}")
        return None

def test_app_creation():
    """Test FastAPI app creation."""
    print("\n🔍 Testing FastAPI app creation...")
    
    try:
        from src.api.main import app
        print(f"  ✓ App created: {type(app)}")
        print(f"  ✓ App routes: {len(app.routes)}")
        return app
    except Exception as e:
        print(f"  ❌ App creation failed: {e}")
        traceback.print_exc()
        return None

def test_file_structure():
    """Test if all required files exist."""
    print("\n🔍 Testing file structure...")

    required_files = [
        "src/__init__.py",
        "src/api/__init__.py",
        "src/api/main.py",
        "src/config.py",
        "src/services/__init__.py",
        "src/services/customer_database.py",
        "requirements.txt"
    ]

    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✓ {file_path}")
        else:
            print(f"  ❌ {file_path} - MISSING")
            missing_files.append(file_path)

    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
        return False

    print("  ✓ All required files present")
    return True

def test_environment():
    """Test environment variables."""
    print("\n🔍 Testing environment...")

    env_vars = {
        'PORT': os.environ.get('PORT', '8080'),
        'PYTHONPATH': os.environ.get('PYTHONPATH', '/app'),
        'GOOGLE_CLOUD_PROJECT': os.environ.get('GOOGLE_CLOUD_PROJECT', 'Not set'),
    }

    for key, value in env_vars.items():
        print(f"  {key}: {value}")

    return True

def main():
    """Run all tests."""
    print("🚀 Container Startup Test")
    print("=" * 40)

    # Test Python environment
    print(f"Python version: {sys.version}")
    print(f"Python path: {sys.path[:3]}...")
    print(f"Working directory: {os.getcwd()}")

    # Test file structure
    if not test_file_structure():
        print("\n❌ File structure test failed")
        sys.exit(1)

    # Test environment
    test_environment()

    # Test imports
    if not test_imports():
        print("\n❌ Import test failed - container will not start")
        sys.exit(1)

    # Test port
    port = test_port_config()
    if not port:
        print("\n❌ Port configuration failed")
        sys.exit(1)

    # Test app
    app = test_app_creation()
    if not app:
        print("\n❌ App creation failed")
        sys.exit(1)

    print("\n✅ All tests passed! Container should start successfully.")

    # Option to start server or just test
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--start-server":
        print(f"\n🚀 Starting server on port {port}...")
        try:
            import uvicorn
            uvicorn.run(
                app,
                host="0.0.0.0",
                port=port,
                workers=1,
                log_level="info"
            )
        except Exception as e:
            print(f"❌ Server startup failed: {e}")
            traceback.print_exc()
            sys.exit(1)
    else:
        print("\n🎯 Tests completed. Run with --start-server to start the server.")

if __name__ == "__main__":
    main()
