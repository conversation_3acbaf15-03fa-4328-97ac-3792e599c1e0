# Simple Dockerfile for debugging Cloud Run issues
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PORT=8080

# Install minimal dependencies
RUN pip install fastapi uvicorn

# Set working directory
WORKDIR /app

# Copy simple server
COPY simple_server.py .

# Expose port
EXPOSE 8080

# Run simple server
CMD ["python", "simple_server.py"]
