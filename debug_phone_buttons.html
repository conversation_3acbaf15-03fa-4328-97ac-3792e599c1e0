<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug Phone Buttons</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .phone-preview {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 20px;
            padding: 20px;
            color: white;
            text-align: center;
            margin: 20px 0;
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 1rem;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .call-btn-test {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
        }
        .answer-test {
            background: #4CAF50;
            color: white;
        }
        .decline-test {
            background: #f44336;
            color: white;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔧 Phone Button Debug Tool</h1>
    
    <div class="test-section">
        <h2>1. Basic Button Test</h2>
        <p>Test if basic buttons work in your browser:</p>
        <button class="test-btn" onclick="testBasicClick()">Click Me - Basic Test</button>
        <button class="test-btn" onclick="alert('Alert test works!')">Alert Test</button>
        <div id="basicResult" class="log" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Phone Button Style Test</h2>
        <p>Test the same style buttons as the phone interface:</p>
        <div style="text-align: center;">
            <button class="call-btn-test decline-test" onclick="testDeclineStyle()">❌ Decline</button>
            <button class="call-btn-test answer-test" onclick="testAnswerStyle()">✅ Answer</button>
        </div>
        <div id="styleResult" class="log" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Phone Interface Preview</h2>
        <p>Miniature version of the phone interface:</p>
        <div class="phone-preview">
            <div style="font-size: 3rem; margin: 20px 0;">👩‍💼</div>
            <h3>Customer Support</h3>
            <p>Angela from Support</p>
            <p>📞 Incoming Call</p>
            <div style="margin-top: 30px;">
                <button class="call-btn-test decline-test" onclick="testPhoneDecline()">❌</button>
                <button class="call-btn-test answer-test" onclick="testPhoneAnswer()">✅</button>
            </div>
        </div>
        <div id="phoneResult" class="log" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>4. JavaScript Console Test</h2>
        <p>Check if JavaScript is working properly:</p>
        <button class="test-btn" onclick="runConsoleTest()">Run Console Test</button>
        <div id="consoleResult" class="log" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>5. Open Phone Simulations</h2>
        <p>Try opening the phone simulations:</p>
        <button class="test-btn" onclick="openSimpleTest()">Open Simple Phone Test</button>
        <button class="test-btn" onclick="openFullSimulation()">Open Full Phone Simulation</button>
        <div id="openResult" class="log" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>6. Browser Information</h2>
        <div id="browserInfo" class="log"></div>
    </div>

    <script>
        let testCount = 0;
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `log ${type}`;
            element.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            console.log(message);
        }
        
        function testBasicClick() {
            testCount++;
            log('basicResult', `✅ Basic button click test #${testCount} - SUCCESS!`, 'success');
        }
        
        function testAnswerStyle() {
            log('styleResult', '✅ Answer button (phone style) clicked - SUCCESS!', 'success');
        }
        
        function testDeclineStyle() {
            log('styleResult', '❌ Decline button (phone style) clicked - SUCCESS!', 'success');
        }
        
        function testPhoneAnswer() {
            log('phoneResult', '📞 Phone Answer button clicked - SUCCESS!', 'success');
            log('phoneResult', 'This means the phone interface buttons should work.', 'success');
        }
        
        function testPhoneDecline() {
            log('phoneResult', '📞 Phone Decline button clicked - SUCCESS!', 'success');
        }
        
        function runConsoleTest() {
            try {
                console.log('Console test - this should appear in browser console');
                console.error('Error test - this should appear as error in console');
                console.warn('Warning test - this should appear as warning in console');
                
                log('consoleResult', '✅ Console logging works - check browser console (F12)', 'success');
                log('consoleResult', 'If you see messages in console, JavaScript is working properly.', 'success');
            } catch (error) {
                log('consoleResult', `❌ Console test failed: ${error.message}`, 'error');
            }
        }
        
        function openSimpleTest() {
            try {
                const testWindow = window.open('simple_phone_test.html', '_blank', 'width=400,height=700');
                if (testWindow) {
                    log('openResult', '✅ Simple phone test opened successfully', 'success');
                } else {
                    log('openResult', '❌ Failed to open simple test - check popup blocker', 'error');
                }
            } catch (error) {
                log('openResult', `❌ Error opening simple test: ${error.message}`, 'error');
            }
        }
        
        function openFullSimulation() {
            try {
                const simWindow = window.open('phone_call_simulation.html', '_blank', 'width=400,height=700');
                if (simWindow) {
                    log('openResult', '✅ Full phone simulation opened successfully', 'success');
                } else {
                    log('openResult', '❌ Failed to open simulation - check popup blocker', 'error');
                }
            } catch (error) {
                log('openResult', `❌ Error opening simulation: ${error.message}`, 'error');
            }
        }
        
        // Initialize browser info
        window.addEventListener('load', () => {
            const browserInfo = document.getElementById('browserInfo');
            browserInfo.innerHTML = `
Browser: ${navigator.userAgent}
Screen: ${screen.width}x${screen.height}
Window: ${window.innerWidth}x${window.innerHeight}
Touch Support: ${('ontouchstart' in window) ? 'Yes' : 'No'}
JavaScript: Enabled ✅
Local Time: ${new Date().toLocaleString()}
            `;
            
            console.log('Debug page loaded successfully');
            console.log('Browser:', navigator.userAgent);
        });
    </script>
</body>
</html>
