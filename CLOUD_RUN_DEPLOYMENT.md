# 🚀 Google Cloud Run Deployment Guide

## Why Cloud Run is Perfect for This AI System

### ✅ **Key Benefits:**

1. **Serverless & Cost-Effective**
   - Scales to zero when not in use
   - Pay only for actual requests
   - Auto-scales during high call volumes

2. **Native Google Cloud Integration**
   - Seamless BigQuery integration (already configured)
   - Built-in Google Cloud Speech/Text-to-Speech APIs
   - Easy Cloud Storage integration for audio files

3. **Production-Ready Features**
   - Built-in load balancing
   - HTTPS by default
   - Global CDN
   - Automatic health checks

4. **Perfect for AI Workloads**
   - 2GB memory for AI processing
   - 2 CPU cores for concurrent requests
   - 300-second timeout for complex operations
   - 100 concurrent requests per instance

## 🚀 **Quick Deployment**

### **Option 1: One-Click Deployment**
```bash
./deploy-to-cloud-run.sh
```

### **Option 2: Manual Deployment**

1. **Set up Google Cloud Project:**
```bash
export GOOGLE_CLOUD_PROJECT="your-project-id"
gcloud config set project $GOOGLE_CLOUD_PROJECT
```

2. **Enable Required APIs:**
```bash
gcloud services enable \
    cloudbuild.googleapis.com \
    run.googleapis.com \
    containerregistry.googleapis.com \
    bigquery.googleapis.com \
    speech.googleapis.com \
    texttospeech.googleapis.com
```

3. **Build and Deploy:**
```bash
# Build the container
gcloud builds submit --tag gcr.io/$GOOGLE_CLOUD_PROJECT/ai-customer-support

# Deploy to Cloud Run
gcloud run deploy ai-customer-support \
    --image gcr.io/$GOOGLE_CLOUD_PROJECT/ai-customer-support \
    --region us-central1 \
    --platform managed \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 2 \
    --concurrency 100 \
    --max-instances 10 \
    --min-instances 0 \
    --timeout 300 \
    --set-env-vars "GOOGLE_CLOUD_PROJECT=$GOOGLE_CLOUD_PROJECT,BIGQUERY_DATASET=customer_support" \
    --port 8080
```

## 🔧 **Configuration**

### **Environment Variables:**
- `GOOGLE_CLOUD_PROJECT`: Your Google Cloud project ID
- `BIGQUERY_DATASET`: BigQuery dataset name (default: customer_support)
- `GOOGLE_CLOUD_REGION`: Deployment region (default: us-central1)
- `PORT`: Container port (default: 8080)

### **Resource Allocation:**
- **Memory**: 2GB (optimal for AI processing)
- **CPU**: 2 cores (handles concurrent requests)
- **Concurrency**: 100 requests per instance
- **Timeout**: 300 seconds (for complex AI operations)
- **Auto-scaling**: 0-10 instances

## 🧪 **Testing Your Deployment**

Once deployed, test your endpoints:

### **1. Health Check:**
```bash
curl https://your-service-url/health
```

### **2. Process Customer Message:**
```bash
curl -X POST "https://your-service-url/process-message" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "CUST_001",
    "content": "my orders",
    "channel": "web"
  }'
```

### **3. Voice Processing:**
```bash
curl -X POST "https://your-service-url/voice/process-voice-message" \
  -F "audio=@voice_message.wav" \
  -F "customer_id=CUST_001"
```

### **4. Schedule Outbound Call:**
```bash
curl -X POST "https://your-service-url/outbound/schedule-call" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "CUST_001",
    "purpose": "follow_up",
    "priority": 5
  }'
```

## 📊 **Monitoring & Logging**

### **View Logs:**
```bash
gcloud run services logs read ai-customer-support --region=us-central1
```

### **Monitor Performance:**
- Go to Cloud Console → Cloud Run → ai-customer-support
- View metrics: requests, latency, errors, CPU/memory usage

### **Set up Alerts:**
```bash
# Create alerting policy for high error rate
gcloud alpha monitoring policies create --policy-from-file=alerting-policy.yaml
```

## 🔐 **Security & Best Practices**

### **1. Authentication (Optional):**
```bash
# Require authentication
gcloud run services update ai-customer-support \
    --region=us-central1 \
    --no-allow-unauthenticated
```

### **2. Custom Domain:**
```bash
# Map custom domain
gcloud run domain-mappings create \
    --service=ai-customer-support \
    --domain=api.yourcompany.com \
    --region=us-central1
```

### **3. VPC Connector (for private resources):**
```bash
# Create VPC connector for private BigQuery access
gcloud compute networks vpc-access connectors create ai-support-connector \
    --region=us-central1 \
    --subnet=default \
    --subnet-project=$GOOGLE_CLOUD_PROJECT
```

## 💰 **Cost Optimization**

### **Expected Costs:**
- **Low traffic** (< 1000 requests/day): ~$5-10/month
- **Medium traffic** (10,000 requests/day): ~$20-50/month
- **High traffic** (100,000 requests/day): ~$100-200/month

### **Cost Optimization Tips:**
1. **Use min-instances: 0** (scales to zero)
2. **Optimize container startup time**
3. **Use request-based pricing**
4. **Monitor and set budget alerts**

## 🔄 **CI/CD Pipeline**

### **GitHub Actions Integration:**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Cloud Run
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - uses: google-github-actions/setup-gcloud@v0
      with:
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ secrets.GCP_PROJECT_ID }}
    - run: gcloud builds submit --config cloudbuild.yaml
```

## 🚨 **Troubleshooting**

### **Common Issues:**

1. **Container fails to start:**
   - Check logs: `gcloud run services logs read ai-customer-support`
   - Verify PORT environment variable
   - Check health endpoint

2. **BigQuery connection issues:**
   - Verify service account permissions
   - Check dataset exists
   - Validate environment variables

3. **High latency:**
   - Increase CPU allocation
   - Optimize container startup
   - Use min-instances > 0 for warm instances

4. **Memory issues:**
   - Increase memory allocation
   - Monitor memory usage in Cloud Console
   - Optimize AI model loading

## 📈 **Scaling Considerations**

### **For High Volume:**
- **Increase max-instances**: Up to 100
- **Use min-instances**: 2-5 for warm starts
- **Optimize concurrency**: Test optimal value
- **Consider Cloud Run Jobs**: For batch processing

### **Global Deployment:**
- Deploy to multiple regions
- Use Cloud Load Balancer
- Implement geo-routing

## 🎯 **Next Steps**

1. **Set up BigQuery tables** for customer data
2. **Configure Twilio** for real phone calls
3. **Implement monitoring** and alerting
4. **Set up CI/CD pipeline**
5. **Configure custom domain**
6. **Add authentication** if needed

Your AI Customer Support System is now production-ready on Google Cloud Run! 🎉